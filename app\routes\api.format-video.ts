import { json } from "@remix-run/node";
import { formatVideoForPlatform } from "~/utils/socialMediaFormatter";

export async function action({ request }: { request: Request }) {
  try {
    const { videoUrl, platform, testimonialId } = await request.json();

    if (!videoUrl || !platform || !testimonialId) {
      return json({ error: "Missing required parameters" }, { status: 400 });
    }

    // Format the video for the specific platform
    const formattedVideo = await formatVideoForPlatform(videoUrl, {
      platform,
      addBranding: true,
      addCaptions: false
    });

    return json({
      success: true,
      url: formattedVideo.url,
      platform: formattedVideo.platform,
      specs: formattedVideo.specs,
      downloadUrl: formattedVideo.downloadUrl
    });

  } catch (error) {
    console.error("Error formatting video:", error);
    return json({ error: "Failed to format video" }, { status: 500 });
  }
}
