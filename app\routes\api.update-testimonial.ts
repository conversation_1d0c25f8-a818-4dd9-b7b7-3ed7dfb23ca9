import { json } from "@remix-run/node";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function action({ request }: { request: Request }) {
  try {
    const { testimonialId, updates } = await request.json();

    if (!testimonialId) {
      return json({ error: "Missing testimonial ID" }, { status: 400 });
    }

    if (!updates || typeof updates !== 'object') {
      return json({ error: "Missing or invalid updates" }, { status: 400 });
    }

    // Validate allowed fields to update
    const allowedFields = ['addToProductPage'];
    const validUpdates: any = {};

    for (const [key, value] of Object.entries(updates)) {
      if (allowedFields.includes(key)) {
        validUpdates[key] = value;
      }
    }

    if (Object.keys(validUpdates).length === 0) {
      return json({ error: "No valid fields to update" }, { status: 400 });
    }

    // Update the testimonial
    const updatedTestimonial = await prisma.testimonial.update({
      where: {
        id: testimonialId
      },
      data: validUpdates
    });

    return json({ 
      success: true, 
      testimonial: updatedTestimonial 
    });

  } catch (error) {
    console.error("Error updating testimonial:", error);
    
    // Handle specific Prisma errors
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      return json({ error: "Testimonial not found" }, { status: 404 });
    }
    
    return json({ error: "Failed to update testimonial" }, { status: 500 });
  }
}
