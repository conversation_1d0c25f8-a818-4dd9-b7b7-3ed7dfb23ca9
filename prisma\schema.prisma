// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

model Session {
  id          String    @id
  shop        String
  state       String
  isOnline    <PERSON>an   @default(false)
  scope       String?
  expires     DateTime?
  accessToken String
  userId      BigInt?
}

model Testimonial {
  id          String   @id @default(cuid())
  shop        String   // Shopify store domain // Connet to store
  customerId  String   // Shopify customer ID
  customerName String  @default("Test")// Shopify cusotmer Name
  videoUrl    String   // URL to the video testimonial
  //cloudfareURL String @default("Test")// Cloudflare URL
  productId   String  // Optional Shopify product ID (null for store testimonials)
  productName String   @default("Test")// Product name
  createdAt   DateTime @default(now())
  addToProductPage Boolean @default(false) // Flag to indicate if testimonial should be shown on product page
}


// Store Setting
// Testimonial bar - product: [list of videos that store owner has chosen]