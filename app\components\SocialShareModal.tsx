import { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>, 
  InlineStack, 
  BlockStack, 
  Card,
  Spinner,
  Badge,
  Icon,
  Divider
} from '@shopify/polaris';
import {
  ShareIcon,
  ExportIcon,
  ExternalIcon,
  CheckIcon
} from '@shopify/polaris-icons';

interface SocialShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  testimonial: {
    id: string;
    videoUrl: string;
    customerName: string;
    productName: string;
  };
  storeName?: string;
}

interface PlatformOption {
  id: string;
  name: string;
  icon: string;
  aspectRatio: string;
  color: string;
  action: 'download' | 'share';
  note: string;
}

const PLATFORMS: PlatformOption[] = [
  {
    id: 'instagram',
    name: 'Instagram',
    icon: '📷',
    aspectRatio: '9:16 (Stories/Reels)',
    color: 'bg-gradient-to-r from-purple-500 to-pink-500',
    action: 'download',
    note: 'Download optimized video for Instagram'
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    icon: '🎵',
    aspectRatio: '9:16 (Vertical)',
    color: 'bg-black',
    action: 'download',
    note: 'Download optimized video for TikTok'
  },
  {
    id: 'twitter',
    name: 'X (Twitter)',
    icon: '🐦',
    aspectRatio: '16:9 (Landscape)',
    color: 'bg-black',
    action: 'share',
    note: 'Share directly on X'
  }
];

export function SocialShareModal({ isOpen, onClose, testimonial, storeName }: SocialShareModalProps) {
  const [processingPlatform, setProcessingPlatform] = useState<string | null>(null);
  const [processedVideos, setProcessedVideos] = useState<Record<string, string>>({});

  const generateCaption = (platform: string) => {
    const baseMessage = `Amazing testimonial for ${testimonial.productName}! 🌟`;
    
    switch (platform) {
      case 'instagram':
        return `${baseMessage}\n\n✨ Real customer review from ${testimonial.customerName}\n${storeName ? `🛍️ Shop at ${storeName}` : ''}\n\n#testimonial #review #customerreview`;
      
      case 'tiktok':
        return `${baseMessage} Real review from ${testimonial.customerName}! ${storeName ? `Shop at ${storeName}` : ''} #testimonial #review`;
      
      case 'twitter':
        return `${baseMessage} Real customer review from ${testimonial.customerName}. ${storeName ? `Shop at ${storeName}` : ''}`;
      
      default:
        return `${baseMessage} Real customer review from ${testimonial.customerName}.`;
    }
  };

  const handlePlatformAction = async (platform: PlatformOption) => {
    setProcessingPlatform(platform.id);
    
    try {
      // Format video for the specific platform
      const response = await fetch('/api/format-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          videoUrl: testimonial.videoUrl,
          platform: platform.id,
          testimonialId: testimonial.id
        })
      });

      if (!response.ok) {
        throw new Error('Failed to format video');
      }

      const data = await response.json();
      
      if (platform.action === 'download') {
        // Create download link
        const link = document.createElement('a');
        link.href = data.url;
        link.download = `${testimonial.productName}_${platform.id}_testimonial.mp4`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        setProcessedVideos(prev => ({
          ...prev,
          [platform.id]: data.url
        }));
      } else {
        // Open share URL
        const caption = generateCaption(platform.id);
        const shareUrl = getShareUrl(platform.id, data.url, caption);
        window.open(shareUrl, '_blank', 'width=600,height=400');
      }
      
    } catch (error) {
      console.error(`Error processing video for ${platform.name}:`, error);
      // You could show an error toast here
    } finally {
      setProcessingPlatform(null);
    }
  };

  const getShareUrl = (platform: string, videoUrl: string, caption: string) => {
    const encodedCaption = encodeURIComponent(caption);
    const encodedUrl = encodeURIComponent(videoUrl);
    
    switch (platform) {
      case 'twitter':
        return `https://twitter.com/intent/tweet?text=${encodedCaption}&url=${encodedUrl}`;
      case 'facebook':
        return `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedCaption}`;
      default:
        return videoUrl;
    }
  };

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      title="Share Testimonial"
      primaryAction={{
        content: 'Close',
        onAction: onClose,
      }}
      large
    >
      <Modal.Section>
        <BlockStack gap="400">
          <Box>
            <Text variant="headingMd" as="h3">
              Share testimonial for {testimonial.productName}
            </Text>
            <Text variant="bodyMd" color="subdued">
              Customer: {testimonial.customerName}
            </Text>
          </Box>

          <Divider />

          <Text variant="headingMd" as="h4">
            Choose Platform
          </Text>

          <BlockStack gap="300">
            {PLATFORMS.map((platform) => (
              <Card key={platform.id}>
                <Box padding="400">
                  <InlineStack align="space-between" blockAlign="center">
                    <InlineStack gap="300" blockAlign="center">
                      <Box>
                        <Text variant="headingMd" as="h5">
                          {platform.icon} {platform.name}
                        </Text>
                        <Text variant="bodyMd" color="subdued">
                          {platform.aspectRatio} • {platform.note}
                        </Text>
                      </Box>
                    </InlineStack>

                    <InlineStack gap="200">
                      {processedVideos[platform.id] && (
                        <Badge tone="success">
                          <Icon source={CheckIcon} /> Ready
                        </Badge>
                      )}
                      
                      <Button
                        onClick={() => handlePlatformAction(platform)}
                        loading={processingPlatform === platform.id}
                        icon={platform.action === 'download' ? ExportIcon : ExternalIcon}
                        variant="primary"
                      >
                        {processingPlatform === platform.id ? (
                          'Processing...'
                        ) : platform.action === 'download' ? (
                          'Download'
                        ) : (
                          'Share'
                        )}
                      </Button>
                    </InlineStack>
                  </InlineStack>
                </Box>
              </Card>
            ))}
          </BlockStack>

          <Box>
            <Text variant="bodyMd" color="subdued">
              💡 Tip: Videos are automatically optimized for each platform's requirements including aspect ratio, duration, and file size limits.
            </Text>
          </Box>
        </BlockStack>
      </Modal.Section>
    </Modal>
  );
}
