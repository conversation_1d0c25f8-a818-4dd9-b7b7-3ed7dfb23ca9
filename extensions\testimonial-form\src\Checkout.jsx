
import {
  reactExtension,
  BlockStack,
  View,
  Heading,
  Text,
  ChoiceList,
  Choice,
  Button,
  Link,
  useStorage,
  useApi,
  useEmail
} from '@shopify/ui-extensions-react/checkout';
import {useCallback, useEffect, useState} from 'react';
// Allow the attribution survey to display on the thank you page.
const thankYouBlock = reactExtension("purchase.thank-you.block.render", () => <Attribution />);
export { thankYouBlock };

// const orderDetailsBlock = reactExtension("customer-account.order-status.block.render", () => <ProductReview />);
// export { orderDetailsBlock };
function Attribution() {
  // Store into local storage if the testimonial request was completed by the customer.
  const [testimonialRequested, setTestimonialRequested] = useStorageState('testimonial-requested')

  const {shop} = useApi();
  const customer = useEmail();
  console.log(shop, customer);

  const custID = customer || "Dummy";

  console.log(custID);

  function handleSkip() {
    setTestimonialRequested(true);
  }

  function handleYes() {
    setTestimonialRequested(true);
  }

  // Hides the request if already submitted
  if (testimonialRequested.loading || testimonialRequested.data === true) {
    return null;
  }

  return (
    <View border="base" padding="base" borderRadius="base">
      <BlockStack>
        <Heading>Thanks for placing another order! 🎉</Heading>
        <Text>We see you're a repeating customer on our store. Would you like to give testimonials of older purchase for a $10 discount on the next one?</Text>

        <BlockStack spacing="tight" alignment="center">
          {custID && shop.name ? (
            <Link
              to={`https://gl-treatments-royal-abilities.trycloudflare.com/testimonials?storeDomain=${shop.name}&custID=${custID}`}
              external={true}
              onPress={handleYes}
              accessibilityLabel="Yes, give testimonial for discount"
            >
              <Button kind="primary">
                Yes
              </Button>
            </Link>
          ) : (
            <Button
              onPress={handleYes}
              accessibilityLabel="Yes, give testimonial for discount"
              kind="primary"
              disabled
            >
              Yes (Loading...)
            </Button>
          )}

          <Button
            kind="plain"
            onPress={handleSkip}
            accessibilityLabel="No, skip testimonial"
          >
            No
          </Button>
        </BlockStack>
      </BlockStack>
    </View>
  );
}



/**
 * Returns a piece of state that is persisted in local storage, and a function to update it.
 * The state returned contains a `data` property with the value, and a `loading` property that is true while the value is being fetched from storage.
 */
function useStorageState(key) {
  const storage = useStorage();
  const [data, setData] = useState()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function queryStorage() {
      const value = await storage.read(key)
      setData(value);
      setLoading(false)
    }

    queryStorage();
  }, [setData, setLoading, storage, key])

  const setStorage = useCallback((value) => {
    storage.write(key, value)
  }, [storage, key])

  return [{data, loading}, setStorage]
}
