import { getSignedVideoUrl } from './storage';

// Simplified video processor for social media formats
// In production, you would use FFmpeg for actual video processing

// // Configure FFmpeg to use the static binary
// if (ffmpegStatic) {
//   ffmpeg.setFfmpegPath(ffmpegStatic);
//   console.log('FFmpeg configured with static binary:', ffmpegStatic);
// } else {
//   console.warn('FFmpeg static binary not found, using system FFmpeg');
// }

// // Test FFmpeg availability by checking version
// function testFFmpegAvailability(): Promise<boolean> {
//   return new Promise((resolve) => {
//     ffmpeg.getAvailableFormats((err, _formats) => {
//       if (err) {
//         console.error('❌ FFmpeg test failed:', err.message);
//         resolve(false);
//       } else {
//         console.log('✅ FFmpeg is working correctly');
//         resolve(true);
//       }
//     });
//   });
// }

// // Use process.cwd() for directory resolution in Node.js
// const __dirname = process.cwd();

// const S3_BUCKET = process.env.S3_BUCKET || 'testtrustpeer';
// const S3_REGION = process.env.S3_REGION || 'us-east-1';

// let s3Client: S3Client | null = null;
// if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
//   s3Client = new S3Client({
//     region: S3_REGION,
//     credentials: {
//       accessKeyId: process.env.AWS_ACCESS_KEY_ID,
//       secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
//     },
//   });
// }

// const openai = new OpenAI({
//   apiKey: process.env.OPENAI_API_KEY,
// });

// interface ProductInfo {
//   name: string;
//   price: string;
//   keyBenefits: string[];
//   storeName: string;
// }





// async function transcribeAudio(filePath: string): Promise<string> {
//   const tempDir = path.join(__dirname, 'temp');
//   const audioPath = path.join(tempDir, `temp_audio_${Date.now()}.mp3`);

//   try {
//     // Check if input file exists
//     if (!fs.existsSync(filePath)) {
//       throw new Error(`Input video file not found: ${filePath}`);
//     }

//     await new Promise((resolve, reject) => {
//       ffmpeg(filePath)
//         .noVideo()
//         .audioCodec('libmp3lame')
//         .audioFrequency(16000) // Whisper works best with 16kHz
//         .audioChannels(1) // Mono audio
//         .output(audioPath)
//         .on('end', resolve)
//         .on('error', (err) => {
//           console.error('FFmpeg audio extraction error:', err);
//           reject(new Error(`Audio extraction failed: ${err.message}`));
//         })
//         .run();
//     });

//     if (!openai.apiKey) {
//       throw new Error('OpenAI API key not configured');
//     }

//     const file = fs.createReadStream(audioPath);
//     const response = await openai.audio.transcriptions.create({
//       file: file as any,
//       model: 'whisper-1',
//     });

//     return response.text;
//   } catch (error) {
//     console.error('Transcription error:', error);
//     throw error;
//   } finally {
//     // Clean up temp audio file
//     if (fs.existsSync(audioPath)) {
//       fs.unlinkSync(audioPath);
//     }
//   }
// }

// async function generateScript(transcript: string, productInfo: ProductInfo): Promise<{ hook: string; quote: string; cta: string; }> {
//   try {
//     if (!openai.apiKey) {
//       throw new Error('OpenAI API key not configured');
//     }

//     const prompt = `Turn the following testimonial into an Instagram Reel script. Extract a compelling HOOK, a clear QUOTE, and a strong CTA.

// Format your response as:
// HOOK: [compelling hook text]
// QUOTE: [testimonial quote]
// CTA: [call to action]

// Product: ${productInfo.name || 'Product'}, Price: ${productInfo.price || 'N/A'}, Store: ${productInfo.storeName || 'Store'}.

// Testimonial: ${transcript}`;

//     const response = await openai.chat.completions.create({
//       model: 'gpt-4',
//       messages: [{ role: 'user', content: prompt }],
//       max_tokens: 500,
//       temperature: 0.7,
//     });

//     const result = response.choices[0].message?.content || '';

//     // Parse the response more robustly
//     const lines = result.split('\n').filter(line => line.trim());
//     let hook = 'Amazing testimonial!';
//     let quote = transcript.substring(0, 100) + '...';
//     let cta = 'Try it today!';

//     for (const line of lines) {
//       if (line.toLowerCase().includes('hook:')) {
//         hook = line.replace(/^.*?hook:\s*/i, '').trim();
//       } else if (line.toLowerCase().includes('quote:')) {
//         quote = line.replace(/^.*?quote:\s*/i, '').trim();
//       } else if (line.toLowerCase().includes('cta:')) {
//         cta = line.replace(/^.*?cta:\s*/i, '').trim();
//       }
//     }

//     return { hook, quote, cta };
//   } catch (error) {
//     console.error('Script generation error:', error);
//     // Return fallback values
//     return {
//       hook: 'Amazing testimonial!',
//       quote: transcript.substring(0, 100) + (transcript.length > 100 ? '...' : ''),
//       cta: 'Try it today!'
//     };
//   }
// }

// // Enhanced video processing with professional effects
// async function enhanceVideoQuality(inputPath: string, outputPath: string): Promise<void> {
//   return new Promise((resolve, reject) => {
//     console.log('Applying professional video enhancements...');

//     ffmpeg(inputPath)
//       .videoFilters([
//         // Color correction and enhancement
//         'eq=contrast=1.2:brightness=0.05:saturation=1.1:gamma=0.95',
//         // Sharpen the image
//         'unsharp=5:5:1.0:5:5:0.0',
//         // Reduce noise
//         'hqdn3d=4:3:6:4.5',
//         // Stabilization (if needed)
//         'deshake',
//         // Enhance skin tones
//         'colorbalance=rs=0.1:gs=-0.05:bs=-0.1:rm=0.05:gm=0:bm=-0.05:rh=-0.05:gh=0.05:bh=0'
//       ])
//       .output(outputPath)
//       .on('end', () => {
//         console.log('Video enhancement completed');
//         resolve();
//       })
//       .on('error', (err) => {
//         console.error('Video enhancement error:', err);
//         reject(err);
//       })
//       .run();
//   });
// }

// // Professional text overlays with animations and styling
// async function addProfessionalOverlays(inputPath: string, outputPath: string, hook: string, quote: string, cta: string): Promise<void> {
//   return new Promise((resolve, reject) => {
//     // Advanced text cleaning and formatting
//     const formatText = (text: string, maxLength: number = 40) => {
//       return text
//         .replace(/[^\w\s!?.-]/g, ' ')
//         .replace(/\s+/g, ' ')
//         .trim()
//         .toUpperCase() // Make text more impactful
//         .substring(0, maxLength);
//     };

//     const formattedHook = formatText(hook, 35);
//     const formattedQuote = formatText(quote, 50);
//     const formattedCta = formatText(cta, 30);

//     console.log('Adding professional overlays...');
//     console.log('Hook:', formattedHook);
//     console.log('Quote:', formattedQuote);
//     console.log('CTA:', formattedCta);

//     // Create complex filter for professional overlays
//     const filterComplex = [
//       // Hook with slide-in animation (0-5 seconds)
//       `drawtext=text='${formattedHook}':fontfile=/Windows/Fonts/arial.ttf:fontsize=36:fontcolor=white:x='if(lt(t,1), -text_w, if(lt(t,2), (t-1)*w-text_w, (w-text_w)/2))':y=80:box=1:boxcolor=0x000000@0.7:boxborderw=15:enable='between(t,0,5)'`,

//       // Quote with fade-in effect (5-12 seconds)
//       `drawtext=text='${formattedQuote}':fontfile=/Windows/Fonts/arial.ttf:fontsize=30:fontcolor=white:x=(w-text_w)/2:y=(h-text_h)/2:box=1:boxcolor=0x000000@0.7:boxborderw=12:alpha='if(lt(t,5), 0, if(lt(t,6), (t-5), 1))':enable='between(t,5,12)'`,

//       // CTA with pulse effect (12-15 seconds)
//       `drawtext=text='${formattedCta}':fontfile=/Windows/Fonts/arial.ttf:fontsize=32:fontcolor=yellow:x=(w-text_w)/2:y=h-120:box=1:boxcolor=0xFF0000@0.8:boxborderw=15:fontsize='32+8*sin(2*PI*t)':enable='between(t,12,15)'`,

//       // Add subtle vignette effect
//       'vignette=angle=PI/4:mode=backward',

//       // Add brand watermark (bottom right)
//       `drawtext=text='TESTIMONIAL':fontfile=/Windows/Fonts/arial.ttf:fontsize=16:fontcolor=white@0.6:x=w-text_w-20:y=h-text_h-20`
//     ].join(',');

//     ffmpeg(inputPath)
//       .complexFilter(filterComplex)
//       .output(outputPath)
//       .on('end', () => {
//         console.log('Professional overlays completed');
//         resolve();
//       })
//       .on('error', (err) => {
//         console.error('Professional overlay error:', err);
//         // Fallback to simple overlays
//         console.log('Falling back to simple overlays...');
//         addSimpleOverlays(inputPath, outputPath, formattedHook, formattedQuote, formattedCta)
//           .then(resolve)
//           .catch(reject);
//       })
//       .run();
//   });
// }

// // Fallback simple overlays
// async function addSimpleOverlays(inputPath: string, outputPath: string, hook: string, quote: string, cta: string): Promise<void> {
//   return new Promise((resolve, reject) => {
//     ffmpeg(inputPath)
//       .videoFilters([
//         `drawtext=text='${hook}':fontsize=32:fontcolor=white:x=(w-text_w)/2:y=80:box=1:boxcolor=black@0.7:boxborderw=10`,
//         `drawtext=text='${quote}':fontsize=28:fontcolor=white:x=(w-text_w)/2:y=(h-text_h)/2:box=1:boxcolor=black@0.7:boxborderw=10`,
//         `drawtext=text='${cta}':fontsize=30:fontcolor=yellow:x=(w-text_w)/2:y=h-100:box=1:boxcolor=red@0.8:boxborderw=10`
//       ])
//       .output(outputPath)
//       .on('end', () => {
//         console.log('Simple overlays completed');
//         resolve();
//       })
//       .on('error', reject)
//       .run();
//   });
// }

export async function processVideoToReel(videoUrl: string, testimonialId: string): Promise<string> {
  try {
    // For now, return the original video URL with signed access
    // In production, this would process the video with FFmpeg
    const signedUrl = await getSignedVideoUrl(videoUrl);
    return signedUrl;
  } catch (error) {
    console.error('Error processing video to reel:', error);
    throw new Error('Failed to process video to reel');
  }
}

//   // Ensure temp directory exists
//   if (!fs.existsSync(tempDir)) {
//     fs.mkdirSync(tempDir, { recursive: true });
//   }

//   const inputPath = path.join(tempDir, `input_${tempId}.mp4`);
//   const trimmedPath = path.join(tempDir, `trimmed_${tempId}.mp4`);
//   const enhancedPath = path.join(tempDir, `enhanced_${tempId}.mp4`);
//   const brandedPath = path.join(tempDir, `branded_${tempId}.mp4`);
//   const outputPath = path.join(tempDir, `output_${tempId}.mp4`);

//   try {
//     // Extract S3 key from URL or use as-is if it's already a key
//     let videoKey: string;

//     if (videoUrl.startsWith('http://') || videoUrl.startsWith('https://')) {
//       try {
//         const url = new URL(videoUrl);
//         // Extract key from S3 URL path, removing leading slash and query parameters
//         videoKey = url.pathname.substring(1).split('?')[0];
//       } catch (error) {
//         console.error('Error parsing video URL:', error);
//         throw new Error('Invalid video URL format');
//       }
//     } else {
//       // Assume it's already an S3 key like 'testimonials/store/product/video.mp4'
//       videoKey = videoUrl;
//     }

//     console.log('Attempting to download video with key:', videoKey);

//     if (!s3Client) {
//       throw new Error('S3 client not configured. Please check AWS credentials.');
//     }

//     // Download video from S3
//     const command = new GetObjectCommand({
//       Bucket: S3_BUCKET,
//       Key: videoKey,
//     });

//     const response = await s3Client.send(command);

//     if (!response.Body) {
//       throw new Error('No video content received from S3');
//     }

//     // Convert stream to buffer
//     const chunks: Uint8Array[] = [];
//     const stream = response.Body as any;

//     for await (const chunk of stream) {
//       chunks.push(chunk);
//     }

//     const buffer = Buffer.concat(chunks);

//     // Save video to temp file
//     fs.writeFileSync(inputPath, buffer);

//     // 1. Transcribe
//     const transcript = await transcribeAudio(inputPath);
//     const { hook, quote, cta } = await generateScript(transcript, {
//       name: '',
//       price: '',
//       keyBenefits: [],
//       storeName: '',
//     });

//     // 2. Trim video to 15s and resize with professional settings
//     console.log('Trimming and resizing video with professional settings...');
//     await new Promise((resolve, reject) => {
//       ffmpeg(inputPath)
//         .setStartTime('00:00:00')
//         .setDuration(15)
//         .size('1080x1920')
//         .aspect('9:16')
//         .videoCodec('libx264')
//         .audioCodec('aac')
//         .videoBitrate('3000k') // Higher bitrate for quality
//         .audioBitrate('192k')  // Higher audio quality
//         .outputOptions([
//           '-preset', 'slow',    // Better compression
//           '-crf', '18',         // High quality (lower = better)
//           '-pix_fmt', 'yuv420p' // Compatibility
//         ])
//         .output(trimmedPath)
//         .on('end', () => {
//           console.log('Video trimming completed');
//           resolve(undefined);
//         })
//         .on('error', (err) => {
//           console.error('FFmpeg trimming error:', err);
//           reject(err);
//         })
//         .on('progress', (progress) => {
//           console.log('Trimming progress:', progress.percent + '%');
//         })
//         .run();
//     });

//     // 3. Apply professional video enhancement
//     const enhancedPath = path.join(tempDir, `enhanced_${tempId}.mp4`);
//     try {
//       await enhanceVideoQuality(trimmedPath, enhancedPath);
//     } catch (enhanceError) {
//       console.warn('Video enhancement failed, using original:', enhanceError);
//       // Copy original if enhancement fails
//       fs.copyFileSync(trimmedPath, enhancedPath);
//     }

//     // 4. Add professional overlays
//     console.log('Adding professional overlays...');
//     console.log('Hook:', hook);
//     console.log('Quote:', quote);
//     console.log('CTA:', cta);

//     try {
//       // Try professional overlays first
//       await addProfessionalOverlays(enhancedPath, brandedPath, hook, quote, cta);
//     } catch (overlayError) {
//       console.warn('Professional overlays failed, trying simple overlays:', overlayError);
//       try {
//         // Fallback to simple overlays
//         await addSimpleOverlays(enhancedPath, brandedPath, hook, quote, cta);
//       } catch (simpleError) {
//         console.warn('All overlays failed, proceeding without overlays:', simpleError);
//         // Final fallback: just copy the enhanced video
//         fs.copyFileSync(enhancedPath, brandedPath);
//       }
//     }

//     // 5. Final professional processing with audio enhancement
//     console.log('Final professional processing...');
//     await new Promise((resolve, reject) => {
//       ffmpeg(brandedPath)
//         .audioFilters([
//           // Audio enhancement filters
//           'highpass=f=80',           // Remove low-frequency noise
//           'lowpass=f=8000',          // Remove high-frequency noise
//           'compand=attacks=0.3:decays=0.8:points=-80/-80|-45/-15|-27/-9|0/-7|20/-7', // Dynamic range compression
//           'volume=1.2',              // Slight volume boost
//           'aresample=44100'          // Ensure consistent sample rate
//         ])
//         .outputOptions([
//           // Video encoding for maximum quality
//           '-c:v', 'libx264',
//           '-preset', 'slow',         // Best compression efficiency
//           '-crf', '16',              // Very high quality (16 is near-lossless)
//           '-profile:v', 'high',      // H.264 high profile
//           '-level', '4.1',           // H.264 level for compatibility

//           // Audio encoding
//           '-c:a', 'aac',
//           '-b:a', '256k',            // High quality audio
//           '-ar', '44100',            // Standard sample rate
//           '-ac', '2',                // Stereo

//           // Container optimization
//           '-movflags', '+faststart', // Web optimization
//           '-pix_fmt', 'yuv420p',     // Compatibility

//           // Additional quality settings
//           '-g', '30',                // GOP size for better seeking
//           '-keyint_min', '30',       // Minimum keyframe interval
//           '-sc_threshold', '0',      // Disable scene change detection
//           '-b_strategy', '2',        // B-frame strategy
//           '-bf', '3',                // Max B-frames
//           '-refs', '3'               // Reference frames
//         ])
//         .output(outputPath)
//         .on('end', () => {
//           console.log('Professional processing completed');
//           resolve(undefined);
//         })
//         .on('error', (err) => {
//           console.error('Professional processing error:', err);
//           // Fallback to simpler encoding
//           console.log('Falling back to standard processing...');
//           ffmpeg(brandedPath)
//             .outputOptions([
//               '-c:v', 'libx264',
//               '-preset', 'medium',
//               '-crf', '20',
//               '-c:a', 'aac',
//               '-b:a', '192k',
//               '-movflags', '+faststart',
//               '-pix_fmt', 'yuv420p'
//             ])
//             .output(outputPath)
//             .on('end', () => resolve(undefined))
//             .on('error', reject)
//             .run();
//         })
//         .run();
//     });

//     // 5. Upload to S3
//     console.log('Uploading to S3...');
//     if (!s3Client) {
//       throw new Error('S3 client not configured for upload');
//     }

//     const fileBuffer = fs.readFileSync(outputPath);
//     const reelKey = `reels/${testimonialId}_${Date.now()}.mp4`;

//     const uploadCommand = new PutObjectCommand({
//       Bucket: S3_BUCKET,
//       Key: reelKey,
//       Body: fileBuffer,
//       ContentType: 'video/mp4'
//     });

//     await s3Client.send(uploadCommand);
//     console.log('Upload completed, S3 key:', reelKey);

//     // 6. Cleanup
//     const filesToCleanup = [inputPath, trimmedPath, enhancedPath, brandedPath, outputPath];
//     filesToCleanup.forEach(file => {
//       if (fs.existsSync(file)) {
//         try {
//           fs.unlinkSync(file);
//           console.log('Cleaned up:', file);
//         } catch (cleanupError) {
//           console.warn('Failed to cleanup file:', file, cleanupError);
//         }
//       }
//     });

//     return reelKey;

//   } catch (error) {
//     console.error('❌ Error creating reel:', error);

//     // Cleanup on error
//     const filesToCleanup = [inputPath, trimmedPath, enhancedPath, brandedPath, outputPath];
//     filesToCleanup.forEach(file => {
//       if (fs.existsSync(file)) {
//         try {
//           fs.unlinkSync(file);
//         } catch (cleanupError) {
//           console.warn('Failed to cleanup file on error:', file, cleanupError);
//         }
//       }
//     });

//     throw new Error(error instanceof Error ? error.message : 'Reel creation failed');
//   }
// }