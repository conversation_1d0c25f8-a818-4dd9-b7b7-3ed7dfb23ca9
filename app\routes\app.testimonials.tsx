import { Page, Layout, <PERSON>Field, Text, Box, Button, Collapsible, BlockStack, Spinner, InlineStack, Badge, <PERSON>over, ActionList, Icon, Tabs, ChoiceList, Filters } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { PrismaClient } from "@prisma/client";
import {ShareIcon, ProductAddIcon, SortIcon, PlayIcon} from '@shopify/polaris-icons';
import { processVideoToReel } from "../utils/videoProcessor";
import { getSignedVideoUrl } from "../utils/storage";
import { SocialShareModal } from "../components/SocialShareModal";

const prisma = new PrismaClient();

export const loader = async () => {
  // Fetch testimonials directly from Prisma
  const testimonials = await prisma.testimonial.findMany({
    orderBy: {
      createdAt: 'desc'
    },
  });

  // Generate signed URLs for each testimonial
  const testimonialsWithSignedUrls = await Promise.all(
    testimonials.map(async (testimonial) => {
      try {
        const signedUrl = await getSignedVideoUrl(testimonial.videoUrl);
        return {
          ...testimonial,
          originalVideoUrl: testimonial.videoUrl, // Keep original S3 key for processing
          videoUrl: signedUrl // Use signed URL for display
        };
      } catch (error) {
        console.error(`Failed to generate signed URL for testimonial ${testimonial.id}:`, error);
        // Keep original URL as fallback
        return {
          ...testimonial,
          originalVideoUrl: testimonial.videoUrl,
          videoUrl: testimonial.videoUrl
        };
      }
    })
  );

  // Group testimonials by productId
  const productMap = new Map();

  testimonialsWithSignedUrls.forEach(testimonial => {
    const productId = testimonial.productId || 'store';
    const productName = productId === 'store' ? 'Store Experience' : (`Product ${productId}` || "Test");

    if (!productMap.has(productId)) {
      productMap.set(productId, {
        id: productId,
        name: productName,
        testimonials: []
      });
    }

    productMap.get(productId).testimonials.push(testimonial);
  });

  const products = Array.from(productMap.values());

  return json({ products });
};

export default function TestimonialsPage() {
  const { products } = useLoaderData<typeof loader>();
  const [searchTerm, setSearchTerm] = useState("");
  const [expandedProducts, setExpandedProducts] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [popoverActive, setPopoverActive] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [actionStatus, setActionStatus] = useState<{id: string, action: string, status: string} | null>(null);
  const [testimonialPages, setTestimonialPages] = useState<Record<string, number>>({});
  const [selectedTab, setSelectedTab] = useState(0);
  const [sortValue, setSortValue] = useState("newest");
  const [processingReel, setProcessingReel] = useState<string | null>(null);
  const [reelUrl, setReelUrl] = useState<Record<string, string>>({});
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [selectedTestimonialForShare, setSelectedTestimonialForShare] = useState<any>(null);
  
  const itemsPerPage = 10;
  const testimonialsPerPage = 6;

  // Get all testimonials flattened for the All Testimonials tab
  const allTestimonials = products.flatMap(product => 
    product.testimonials.map((testimonial: any) => ({
      ...testimonial,
      productName: product.name,
      productId: product.id
    }))
  );

  // Sort testimonials based on selected sort option
  const sortTestimonials = (testimonials: any[]) => {
    if (sortValue === "newest") {
      return [...testimonials].sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    } else {
      return [...testimonials].sort((a, b) => 
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      );
    }
  };

  // Get testimonials based on selected tab
  const getFilteredTestimonials = () => {
    let filteredList = [];
    
    if (selectedTab === 0) { // All Testimonials
      filteredList = sortTestimonials(allTestimonials);
    } else if (selectedTab === 1) { // By Products
      filteredList = filteredProducts.flatMap(product => 
        sortTestimonials(product.testimonials).map((testimonial: any) => ({
          ...testimonial,
          productName: product.name,
          productId: product.id
        }))
      );
    } else if (selectedTab === 2) { // Store Testimonials
      const storeProduct = products.find(p => p.id === 'store');
      filteredList = storeProduct ? 
        sortTestimonials(storeProduct.testimonials).map((testimonial: any) => ({
          ...testimonial,
          productName: "Store Experience",
          productId: 'store'
        })) : [];
    }
    
    return filteredList.filter(testimonial => 
      testimonial.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      testimonial.customerId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      testimonial.shop.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const filteredTestimonials = getFilteredTestimonials();
  const paginatedTestimonials = filteredTestimonials.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(
    (selectedTab === 1 ? filteredProducts.length : filteredTestimonials.length) / itemsPerPage
  );
  
  const totalTestimonials = products.reduce(
    (sum, product) => sum + product.testimonials.length,
    0
  );

  const handleAddToProduct = async (testimonialId: string) => {
    setIsLoading(true);

    try {
      const response = await fetch('/api/update-testimonial', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testimonialId,
          updates: {
            addToProductPage: true
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to update testimonial');
      }

      setActionStatus({
        id: testimonialId,
        action: 'add',
        status: 'success'
      });

      // Clear status after 3 seconds
      setTimeout(() => {
        setActionStatus(null);
      }, 3000);

    } catch (error) {
      console.error('Error updating testimonial:', error);
      setActionStatus({
        id: testimonialId,
        action: 'add',
        status: 'error'
      });

      setTimeout(() => {
        setActionStatus(null);
      }, 3000);
    } finally {
      setIsLoading(false);
    }

    setPopoverActive(null);
  };

  const handleShareSocial = (testimonial: any) => {
    setSelectedTestimonialForShare(testimonial);
    setShareModalOpen(true);
    setPopoverActive(null);
  };

  const handleTransformToReel = async (testimonialId: string, videoUrl: string) => {
    setProcessingReel(testimonialId);
    try {
      const response = await fetch("/api/process-video", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          videoUrl,
          testimonialId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to process video");
      }

      const data = await response.json();
      setReelUrl(prev => ({
        ...prev,
        [testimonialId]: data.url
      }));
    } catch (error) {
      console.error('Failed to transform video:', error);
      setActionStatus({
        id: testimonialId,
        action: 'reel',
        status: 'error'
      });
      
      // Clear error status after 5 seconds
      setTimeout(() => {
        setActionStatus(null);
      }, 5000);
    } finally {
      setProcessingReel(null);
    }
  };

  // Returns status component based on action type and status
  const getStatusComponent = (testimonialId: string) => {
    if (!actionStatus || actionStatus.id !== testimonialId) return null;
    
    if (actionStatus.status === 'success') {
      const message = actionStatus.action === 'add' 
        ? 'Added to product page!' 
        : actionStatus.action === 'share'
        ? 'Shared to social media!'
        : 'Reel created successfully!';
        
      return (
        <Badge tone="success">{message}</Badge>
      );
    } else if (actionStatus.status === 'error') {
      return (
        <Badge tone="critical">Unable to convert video to reels</Badge>
      );
    }
    
    return null;
  };

  // Initialize testimonial pagination or get current page
  const getTestimonialPage = (productId: string) => {
    if (!testimonialPages[productId]) {
      return 1;
    }
    return testimonialPages[productId];
  };

  // Set testimonial pagination for a specific product
  const setTestimonialPage = (productId: string, page: number) => {
    setTestimonialPages(prev => ({
      ...prev,
      [productId]: page
    }));
  };

  const handleTabChange = (selectedTabIndex: number) => {
    setSelectedTab(selectedTabIndex);
    setCurrentPage(1);
  };

  // Render single testimonial card
  const renderTestimonialCard = (testimonial: any, productName?: string) => {
    return (
      <Box
        key={testimonial.id}
        padding="400" 
        background="bg-surface"
        borderWidth="100"
        borderRadius="100"
        shadow="200"
      >
        <BlockStack gap="400">
          <div style={{ 
            aspectRatio: "16/9",
            borderRadius: "8px",
            overflow: "hidden",
            position: "relative"
          }}>
            <video
              controls
              style={{ 
                width: "100%", 
                height: "100%", 
                objectFit: "cover",
                backgroundColor: "#000"
              }}
            >
              <source src={reelUrl[testimonial.id] || testimonial.videoUrl} type="video/mp4" />
              Your browser does not support video playback.
            </video>
          </div>
          
          <InlineStack align="space-between">
            <BlockStack>
              <Text as="dt" variant="bodyMd" fontWeight="bold">
                Customer ID: {testimonial.customerId}
              </Text>
              <Text as="dt" variant="bodyMd">
                Shop: {testimonial.shop}
              </Text>
              {productName && (
                <Text as="dt" variant="bodyMd">
                  Product: {productName}
                </Text>
              )}
              <Text as="dt" variant="bodySm" tone="subdued">
                {new Date(testimonial.createdAt).toLocaleDateString()}
              </Text>
            </BlockStack>
            
            <div style={{ display: "flex", alignItems: "flex-start" }}>
              {getStatusComponent(testimonial.id)}
              
              <InlineStack gap="100">
                <Button
                  icon={ProductAddIcon}
                  onClick={() => handleAddToProduct(testimonial.id)}
                  variant="tertiary"
                  disabled={testimonial.addToProductPage}
                >
                  {testimonial.addToProductPage ? 'Added to Product Page' : 'Add to Product'}
                </Button>
                <Button
                  icon={ShareIcon}
                  onClick={() => handleShareSocial(testimonial)}
                  variant="tertiary"
                >
                  Share
                </Button>
                <Button
                  icon={PlayIcon}
                  onClick={() => handleTransformToReel(testimonial.id, testimonial.originalVideoUrl || testimonial.videoUrl)}
                  variant="primary"
                  loading={processingReel === testimonial.id}
                  disabled={!!reelUrl[testimonial.id]}
                >
                  {reelUrl[testimonial.id] ? 'Reel Ready' : 'Transform to Reel'}
                </Button>
              </InlineStack>
            </div>
          </InlineStack>
        </BlockStack>
      </Box>
    );
  };

  // Render based on selected tab view
  const renderTestimonialView = () => {
    if (selectedTab === 0 || selectedTab === 2) { // All Testimonials or Store Testimonials
      return (
        <div style={{ 
          display: "grid", 
          gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))", 
          gap: "16px",
          padding: "16px"
        }}>
          {paginatedTestimonials.map((testimonial: any) => 
            renderTestimonialCard(testimonial, selectedTab === 0 ? testimonial.productName : undefined)
          )}
        </div>
      );
    } else { // By Products
      return paginatedProducts.map((product) => {
        const currentTestimonialPage = getTestimonialPage(product.id);
        const totalTestimonialPages = Math.ceil(product.testimonials.length / testimonialsPerPage);
        const paginatedTestimonials = sortTestimonials(product.testimonials).slice(
          (currentTestimonialPage - 1) * testimonialsPerPage,
          currentTestimonialPage * testimonialsPerPage
        );
        
        return (
          <Box 
            key={product.id} 
            padding="400" 
            background="bg-fill"
            borderWidth="100"
            borderRadius="100"
            shadow="100"
          >
            <div
              onClick={() => {
                setExpandedProducts(prev =>
                  prev.includes(product.id)
                    ? prev.filter(id => id !== product.id)
                    : [...prev, product.id]
                );
              }}
              style={{ cursor: 'pointer' }}
            >
              <InlineStack align="space-between">
                <InlineStack gap="200">
                  <Text variant="headingMd" as="h3">
                    {product.name}
                  </Text>
                  <Badge>{`${product.testimonials.length} testimonials`}</Badge>
                </InlineStack>
                <Text as="p" variant="bodyMd">
                  {expandedProducts.includes(product.id) ? "▼" : "▲"}
                </Text>
              </InlineStack>
            </div>

            <Collapsible
              open={expandedProducts.includes(product.id)}
              id={`${product.id}-testimonials`}
            >
              <div style={{ 
                display: "grid", 
                gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))", 
                gap: "16px",
                padding: "16px"
              }}>
                {paginatedTestimonials.map((testimonial: any) => 
                  renderTestimonialCard(testimonial)
                )}
              </div>
              
              {totalTestimonialPages > 1 && (
                <Box padding="400">
                  <div style={{ 
                    display: "flex", 
                    justifyContent: "center", 
                    gap: "16px",
                    alignItems: "center"
                  }}>
                    <Button 
                      disabled={currentTestimonialPage === 1}
                      onClick={() => setTestimonialPage(product.id, currentTestimonialPage - 1)}
                    >
                      Previous
                    </Button>
                    <Text as="dt" variant="bodyMd">
                      Page {currentTestimonialPage} of {totalTestimonialPages}
                    </Text>
                    <Button 
                      disabled={currentTestimonialPage === totalTestimonialPages}
                      onClick={() => setTestimonialPage(product.id, currentTestimonialPage + 1)}
                    >
                      Next
                    </Button>
                  </div>
                </Box>
              )}
            </Collapsible>
          </Box>
        );
      });
    }
  };

  return (
    <Page title="Customer Testimonials">
      <Layout>
        <Layout.Section>
          <Box padding="400">
            <InlineStack gap="400" align="space-between">
              <div style={{ flexGrow: 1 }}>
                <TextField
                  label=""
                  value={searchTerm}
                  onChange={setSearchTerm}
                  placeholder="Search testimonials..."
                  autoComplete="off"
                  clearButton
                  onClearButtonClick={() => setSearchTerm("")}
                />
              </div>
              <Popover
                active={popoverActive === "sort"}
                activator={
                  <Button 
                    icon={SortIcon}
                    onClick={() => setPopoverActive(popoverActive === "sort" ? null : "sort")}
                    variant="secondary"
                  >
                    {sortValue === "newest" ? "Newest first" : "Oldest first"}
                  </Button>
                }
                onClose={() => setPopoverActive(null)}
              >
                <ChoiceList
                  title="Sort by"
                  choices={[
                    {label: 'Newest first', value: 'newest'},
                    {label: 'Oldest first', value: 'oldest'}
                  ]}
                  selected={[sortValue]}
                  onChange={(value) => {
                    setSortValue(value[0]);
                    setPopoverActive(null);
                  }}
                />
              </Popover>
            </InlineStack>
          </Box>
        </Layout.Section>

        <Layout.Section>
          <Tabs
            tabs={[
              {
                id: 'all-testimonials',
                content: 'All Testimonials',
                accessibilityLabel: 'All testimonials'
              },
              {
                id: 'by-products',
                content: 'By Products',
                accessibilityLabel: 'Testimonials organized by products'
              },
              {
                id: 'store-testimonials',
                content: 'Store Testimonials',
                accessibilityLabel: 'Store testimonials'
              }
            ]}
            selected={selectedTab}
            onSelect={handleTabChange}
          />
        </Layout.Section>

        <Layout.Section>
          <InlineStack align="space-between">
            <Text variant="bodyMd" as="p" tone="subdued">
              {selectedTab === 1 ? 
                `Showing ${paginatedProducts.length} of ${filteredProducts.length} products with ${totalTestimonials} total testimonials` :
                `Showing ${paginatedTestimonials.length} of ${filteredTestimonials.length} testimonials`
              }
            </Text>
            
            {selectedTab === 1 && (
              <InlineStack gap="200">
                <Button onClick={() => setExpandedProducts(products.map(p => p.id))}>
                  Expand all
                </Button>
                <Button onClick={() => setExpandedProducts([])}>
                  Collapse all
                </Button>
              </InlineStack>
            )}
          </InlineStack>
        </Layout.Section>

        {isLoading && (
          <Layout.Section>
            <Box padding="400" background="bg-fill" borderRadius="100">
              <BlockStack align="center">
                <Spinner size="large" />
                <Text as="dt">Processing your request...</Text>
              </BlockStack>
            </Box>
          </Layout.Section>
        )}

        <Layout.Section>
          {renderTestimonialView()}
        </Layout.Section>

        {totalPages > 1 && (
          <Layout.Section>
            <Box padding="400">
              <div style={{ 
                display: "flex", 
                justifyContent: "center", 
                gap: "16px",
                alignItems: "center"
              }}>
                <Button 
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(p => p - 1)}
                >
                  Previous
                </Button>
                <Text as="dt" variant="bodyMd">
                  Page {currentPage} of {totalPages}
                </Text>
                <Button 
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(p => p + 1)}
                >
                  Next
                </Button>
              </div>
            </Box>
          </Layout.Section>
        )}
      </Layout>

      {/* Social Share Modal */}
      {selectedTestimonialForShare && (
        <SocialShareModal
          isOpen={shareModalOpen}
          onClose={() => {
            setShareModalOpen(false);
            setSelectedTestimonialForShare(null);
          }}
          testimonial={selectedTestimonialForShare}
          storeName="Your Store" // You can get this from the shop data
        />
      )}
    </Page>
  );
}